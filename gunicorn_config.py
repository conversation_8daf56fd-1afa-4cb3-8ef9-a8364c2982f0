# Gunicorn 配置文件 - Socket.IO 测试服务器

# 服务器绑定
bind = "0.0.0.0:8001"

# Worker 配置
worker_class = "uvicorn.workers.UvicornWorker"
workers = 1  # Socket.IO 建议单个 worker
worker_connections = 1000

# 超时设置
timeout = 30
keepalive = 2

# 日志配置
loglevel = "info"
accesslog = "-"  # 输出到 stdout
errorlog = "-"   # 输出到 stderr
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# 进程管理
preload_app = True
max_requests = 1000
max_requests_jitter = 50

# 性能优化
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统（Linux）

# 启动时显示配置
def on_starting(server):
    print("=" * 60)
    print("Socket.IO 测试服务器启动中...")
    print(f"绑定地址: {bind}")
    print(f"Worker 类型: {worker_class}")
    print(f"Worker 数量: {workers}")
    print("=" * 60)

def on_reload(server):
    print("服务器重新加载...")

def worker_int(worker):
    print(f"Worker {worker.pid} 收到中断信号")

def on_exit(server):
    print("Socket.IO 测试服务器关闭")
