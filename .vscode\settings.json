{
    "[css]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[json]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[python]": {
        "editor.codeActionsOnSave": {
            "source.organizeImports": "always"
        },
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.showUnused": false
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[yaml]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.autoIndent": "full", // 这就是default，不要用formatOnPaste,发生冲突
    "editor.bracketPairColorization.enabled": false,
    "editor.fontFamily": "JetBrains Mono Medium, Source Code Pro Medium, Consolas, 'Courier New', monospace",
    "editor.fontSize": 13,
    "editor.fontWeight": "normal",
    "editor.formatOnSave": true,
    "editor.lineHeight": 1.7,
    "editor.semanticHighlighting.enabled": true,
    "files.associations": {
        "*.toml": "toml"
    },
    "files.autoSave": "onFocusChange",
    "files.exclude": {
        "**/__pycache__": true,
        "**/.pytest_cache": true
    },
    "git.enableSmartCommit": true,
    "git.openRepositoryInParentFolders": "always",
    "hexeditor.columnWidth": 16,
    "hexeditor.defaultEndianness": "little",
    "hexeditor.inspectorType": "aside",
    "hexeditor.showDecodedText": true,
    "jupyter.notebookFileRoot": "${workspaceFolder}",
    "notebook.formatOnSave.enabled": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.inlayHints.callArgumentNames": "partial",
    "python.analysis.inlayHints.functionReturnTypes": true,
    "python.analysis.packageIndexDepths": [
        {
            "depth": 5,
            "includeAllSymbols": true,
            "name": ""
        }
    ],
    "python.analysis.typeCheckingMode": "off",
    "redhat.telemetry.enabled": false,
    "sortJSON.contextMenu": {
        "sortJSON": false,
        "sortJSONAlphaNum": true,
        "sortJSONAlphaNumReverse": false,
        "sortJSONKeyLength": false,
        "sortJSONKeyLengthReverse": false,
        "sortJSONReverse": false,
        "sortJSONType": false,
        "sortJSONTypeReverse": false,
        "sortJSONValues": false,
        "sortJSONValuesReverse": false
    },
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.env.windows": {
        "PYTHONPATH": "${workspaceFolder};${env:PYTHONPATH}",
        "PYTHONUTF8": "1"
    },
    "terminal.integrated.suggest.cdPath": "relative",
    "terminal.integrated.suggest.enabled": true,
    "terminal.integrated.suggest.quickSuggestions": {
        "arguments": "on",
        "commands": "on",
        "unknown": "off"
    },
    "workbench.editor.enablePreview": true,
    "xml.server.preferBinary": true
}