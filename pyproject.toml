[project]
name = "quantback"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = "~=3.11.9"
dependencies = [
    "bokeh>=3.7.1",
    "matplotlib>=3.10.1",
    "numpy>=2.1.3",
    "pandas>=2.2.3",
    "scipy>=1.15.2",
    "pyarrow>=19.0.1",
    "pyyaml>=6.0.2",
    "sambo>=1.25.1",
    "joblib>=1.4.2",
    "tensorflow==2.19.0",
    "tensorflow-io-gcs-filesystem==0.31.0",
    "torch>=2.6.0",
    "scikit-learn>=1.6.1",
    "tqdm>=4.67.1",
    "pytest>=8.3.5",
    "ipykernel>=6.29.5",
    "ipywidgets>=8.1.5",
    "tslearn>=0.6.3",
    "sktime[clustering]>=0.36.0",
    "ta-lib>=0.6.3",
    "fastapi[standard]>=0.115.12",
    "sqlalchemy>=2.0.40",
    "pypinyin>=0.54.0",
    "wakepy>=0.10.1",
    "apscheduler~=3.11.0", # 版本4变化较大
    "redis[hiredis]>=5.2.1",
    "tushare>=1.4.21",
    "clickhouse-connect>=0.8.17",
    "akshare>=1.17.6",
    "line-profiler>=4.2.0",
    "uvicorn>=0.34.0",
    "python-socketio>=5.13.0",
    "gunicorn>=23.0.0",
    "waitress>=3.0.2",
    "eventlet>=0.40.1",
]

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = ["F403", "F405", "E722"]

# F403: from {name} import * used; unable to detect undefined names
# F405: {name} may be undefined, or defined from star imports
# E722: Do not use bare except
[tool.ruff.format]
quote-style = "single"
