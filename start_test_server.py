#!/usr/bin/env python3
"""
Socket.IO 测试服务器启动脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'gunicorn',
        'uvicorn',
        'python-socketio'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n安装命令:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def start_server():
    """启动测试服务器"""
    if not check_dependencies():
        return False
    
    print("\n🚀 启动 Socket.IO 测试服务器...")
    print("=" * 60)
    
    # 构建启动命令
    cmd = [
        'gunicorn',
        'test_socket_server:app',
        '-c', 'gunicorn_config.py'
    ]
    
    try:
        # 启动服务器
        process = subprocess.run(cmd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  服务器已停止")
        return True

def show_usage():
    """显示使用说明"""
    print("Socket.IO 测试服务器")
    print("=" * 40)
    print("文件说明:")
    print("  test_socket_server.py  - 主服务器文件")
    print("  gunicorn_config.py     - Gunicorn 配置")
    print("  start_test_server.py   - 启动脚本")
    print()
    print("启动方式:")
    print("  方式1: python start_test_server.py")
    print("  方式2: gunicorn test_socket_server:app -c gunicorn_config.py")
    print()
    print("服务地址:")
    print("  http://localhost:8001")
    print()
    print("测试事件:")
    print("  connect     - 连接事件")
    print("  ping        - Ping-Pong 测试")
    print("  get_stats   - 获取服务器统计")
    print("  echo        - 回声测试")
    print()
    print("前端连接测试:")
    print("  修改 src/utils/socket.js 中的端口为 8001")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_usage()
    else:
        start_server()
