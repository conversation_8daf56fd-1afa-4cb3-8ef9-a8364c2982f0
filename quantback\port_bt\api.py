"""
类似聚宽的交易API接口
"""

import logging
import sys
from datetime import time
from typing import TYPE_CHECKING, Callable, Dict, Optional, Union

import pandas as pd

from quantback.log_setup import ColorizedFormatter
from quantback.market_interface.market_data import get_kline_data
from quantback.utils.decorators import advanced_func_stats

from .context import Context
from .orders import MarketOrderStyle
from .portfolio import OrderCost

if TYPE_CHECKING:
    from .orders import OrderStyle

log = logging.getLogger(__name__)

# 策略函数类型定义
DailyFunc = Callable[[Context], None]


# 全局变量，用于存储当前回测上下文
_current_context: Optional[Context] = None
_current_data: Optional[Dict[str, pd.Series]] = None


def set_context(context: Context):
    """设置当前上下文"""
    global _current_context
    _current_context = context

    setup_logger(logging.DEBUG)


def setup_logger(log_level: int = logging.INFO):
    global log
    # 防止重复添加handler
    if log.handlers:
        return log

    # 设置日志级别
    log.setLevel(log_level)

    def filter(record):
        tradetime = (
            _current_context.current_dt.strftime('%Y-%m-%d %H:%M:%S')
            if _current_context.current_dt
            else 'Initializing'
        )
        record.tradetime = tradetime
        return True

    log.addFilter(filter)

    # 创建console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    # 设置格式化器
    console_handler.setFormatter(
        ColorizedFormatter('[%(colored_levelname)s %(tradetime)s] %(message)s')
    )

    # 添加handler到logger
    log.addHandler(console_handler)

    # 不向上层传递日志
    log.propagate = False


def run_daily(func: DailyFunc, time: Union[str, time] = '9:30'):
    """
    注册每日定时执行的函数

    Args:
        func: 要执行的函数，接受context参数
        time: 执行时间，可以是:
            - 'every_minute': 每分钟执行一次（在交易时间内）
            - '9:30': 具体时间点执行
            - time对象: 具体时间点执行

    Examples:
        run_daily(my_func, time='9:30')  # 每天9:30执行
        run_daily(my_func, time='every_minute')  # 每分钟执行
    """
    # 延迟导入避免循环导入
    from .engine import _register_task

    _register_task(func, time)


def set_current_data(data: Dict[str, pd.Series]):
    """设置当前数据"""
    global _current_data
    _current_data = data


def set_benchmark(security: str):
    """
    设置基准指数

    Args:
        security: 基准指数代码，如 '000300.SH'
    """
    _current_context.benchmark = security
    log.info(f'设置基准指数: {security}')


def set_order_cost(cost: OrderCost, type: str):
    """
    设置交易成本

    Args:
        cost: OrderCost 对象，包含各种费率配置
        type: 类型，目前支持 'stock' (股票)
    """
    # 验证类型参数
    if type not in ['stock', 'fund']:
        raise ValueError(f"不支持的类型: {type}，目前只支持 'stock' 和 'fund'")

    # 设置交易成本到上下文中
    if not hasattr(_current_context, 'order_costs') or _current_context.order_costs is None:
        _current_context.order_costs = {}

    _current_context.order_costs[type] = cost

    log.info(f'设置{type}交易成本: {cost}')
    log.info(f'  买入佣金: 万分之{cost.open_commission * 10000:.1f}')
    log.info(f'  卖出佣金: 万分之{cost.close_commission * 10000:.1f}')
    log.info(f'  卖出印花税: 千分之{cost.close_tax * 1000:.1f}')
    log.info(f'  最低佣金: {cost.min_commission}元')


def order(security: str, volume: int, style: Optional['OrderStyle'] = None) -> Optional[str]:
    """
    下单交易

    Args:
        security: 股票代码
        volume: 委托数量，正数买入，负数卖出
        style: 订单样式，None表示市价单

    Returns:
        Optional[str]: 订单ID
    """
    # 如果没有指定样式，使用市价单
    if style is None:
        style = MarketOrderStyle()
    log.debug(f'order({security}, {volume}, {style})')
    return _current_context.portfolio.order(security, volume, style)


def order_value(security: str, value: float, style: Optional['OrderStyle'] = None) -> Optional[str]:
    """
    按委托金额下单

    Args:
        security: 股票代码
        value: 委托金额，正数买入，负数卖出
        style: 订单样式，None表示市价单

    Returns:
        Optional[str]: 订单ID
    """
    # 如果没有指定样式，使用市价单
    if style is None:
        style = MarketOrderStyle()
    log.debug(f'order_value({security}, {value}, {style})')
    return _current_context.portfolio.order_value(security, value, style)


def order_target(security: str, volume: int, style: Optional['OrderStyle'] = None) -> Optional[str]:
    """
    调整持仓到目标数量

    Args:
        security: 股票代码
        volume: 目标持仓数量
        style: 订单样式，None表示市价单

    Returns:
        Optional[str]: 订单ID
    """
    # 如果没有指定样式，使用市价单
    if style is None:
        style = MarketOrderStyle()
    log.debug(f'order_target({security}, {volume}, {style})')
    return _current_context.portfolio.order_target(security, volume, style)


def order_target_value(
    security: str, value: float, style: Optional['OrderStyle'] = None
) -> Optional[str]:
    """
    调整持仓到目标金额

    Args:
        security: 股票代码
        value: 目标持仓金额
        style: 订单样式，None表示市价单

    Returns:
        Optional[str]: 订单ID
    """
    # 如果没有指定样式，使用市价单
    if style is None:
        style = MarketOrderStyle()
    # 打印函数的调用参数
    log.debug(f'order_target_value({security}, {value}, {style})')  # 打印函数的调用参数
    return _current_context.portfolio.order_target_value(security, value, style)


def cancel_order(order_id: str) -> bool:
    """
    取消订单

    Args:
        order_id: 订单ID

    Returns:
        bool: 是否成功
    """
    return _current_context.portfolio.cancel_portfolio_order(order_id)


def get_current_data() -> Optional[Dict[str, pd.Series]]:
    """
    获取当前数据

    Returns:
        Optional[Dict[str, pd.Series]]: 当前数据
    """
    return _current_data


# @advanced_func_stats
def get_price(*args, **kwargs):
    """
    获取K线数据（get_kline_data的包装函数）
    这是get_kline_data函数的别名，保持与聚宽API的兼容性
    """
    return get_kline_data(*args, **kwargs)
