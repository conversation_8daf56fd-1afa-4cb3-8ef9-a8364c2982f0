import logging
import os
from logging.handlers import RotatingFileHandler


class ColorizedFormatter(logging.Formatter):
    COLORS = {
        logging.DEBUG: '\033[94m',  # Blue
        logging.INFO: '\033[92m',  # Green
        logging.WARNING: '\033[93m',  # Yellow
        logging.ERROR: '\033[91m',  # Red
        logging.CRITICAL: '\033[95m',  # Purple
    }

    def format(self, record):
        color = self.COLORS.get(record.levelno, '')
        record.colored_levelname = f'{color}{record.levelname}\033[0m'
        return super().format(record)


def setup_logging(log_level=logging.INFO, enable_file_handler=False):
    # 创建日志记录器
    root_logger = logging.getLogger()
    # 设置根日志记录器的级别
    root_logger.setLevel(log_level)

    # 创建控制台处理器并设置级别
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)

    # 创建格式化器并添加到控制台处理器
    console_handler.setFormatter(
        ColorizedFormatter('[%(asctime)s %(colored_levelname)s %(threadName)s] %(message)s')
    )

    # 将控制台处理器添加到日志记录器
    root_logger.addHandler(console_handler)

    # 如果启用文件处理器，则创建并添加文件处理器
    if enable_file_handler:
        # 获取项目根目录
        project_root = os.path.dirname(os.path.abspath(__file__))
        log_dir = os.path.join(project_root, 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        log_file = os.path.join(log_dir, 'backend.log')

        # 创建文件处理器并设置级别
        file_handler = RotatingFileHandler(
            filename=log_file, maxBytes=102400, backupCount=10, encoding='utf-8'
        )
        file_handler.setLevel(log_level)

        # 创建格式化器并添加到文件处理器
        file_handler.setFormatter(
            logging.Formatter('[%(asctime)s %(levelname)s %(threadName)s] %(message)s')
        )

        # 将文件处理器添加到日志记录器
        root_logger.addHandler(file_handler)

    return root_logger
