import { io } from 'socket.io-client'

// 记录socket创建时间
const socketCreateTime = performance.now()
console.log('🔌 Socket 创建时间:', new Date().toISOString())

// 测试基础网络延迟（作为对比）
const testNetworkLatency = async () => {
  try {
    const start = performance.now()
    await fetch('http://localhost:8000/socket.io/', {
      method: 'GET',
      mode: 'no-cors', // 避免CORS问题
    })
    const end = performance.now()
    console.log(`🌐 基础HTTP请求延迟: ${(end - start).toFixed(2)} ms`)
  } catch (error) {
    console.log('🌐 HTTP请求测试失败:', error.message)
  }
}

// 异步测试网络延迟
testNetworkLatency()

export const socket = io('http://localhost:8000', {
  transports: ['websocket', 'polling'],
  // 添加一些优化配置
  timeout: 5000,
  forceNew: true,
  // 优化连接参数
  upgrade: true,
  rememberUpgrade: true,
})

// 记录连接开始尝试的时间
let connectAttemptTime = null

// 监听连接开始尝试
socket.on('connect', () => {
  const connectTime = performance.now()
  const totalDuration = connectTime - socketCreateTime
  const connectDuration = connectAttemptTime
    ? connectTime - connectAttemptTime
    : null

  console.log('✅ Socket 连接成功!')
  console.log(`⏱️  总耗时(包含模块加载): ${totalDuration.toFixed(2)} ms`)
  if (connectDuration) {
    console.log(`🚀 纯连接耗时: ${connectDuration.toFixed(2)} ms`)
  }
  console.log('🕐 连接成功时间:', new Date().toISOString())
})

// 监听连接尝试开始
socket.on('connecting', () => {
  connectAttemptTime = performance.now()
  console.log('🔄 开始尝试连接...')
})

// 监听连接失败事件
socket.on('connect_error', (error) => {
  const errorTime = performance.now()
  const totalDuration = errorTime - socketCreateTime
  const connectDuration = connectAttemptTime
    ? errorTime - connectAttemptTime
    : null

  console.log('❌ Socket 连接失败!')
  console.log(`⏱️  总失败耗时: ${totalDuration.toFixed(2)} ms`)
  if (connectDuration) {
    console.log(`🚀 纯连接尝试耗时: ${connectDuration.toFixed(2)} ms`)
  }
  console.log('🔴 连接失败时间:', new Date().toISOString())
  console.log('📋 错误详情:', error)
})

// 监听重连尝试
socket.on('reconnect_attempt', (attemptNumber) => {
  connectAttemptTime = performance.now()
  console.log(`🔄 重连尝试 #${attemptNumber}`)
})

// 添加更详细的调试信息
socket.on('disconnect', (reason) => {
  console.log('🔌 Socket 断开连接:', reason)
})
