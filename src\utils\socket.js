import { io } from 'socket.io-client'

// 记录socket创建时间
const socketCreateTime = performance.now()
console.log('🔌 Socket 创建时间:', new Date().toISOString())

// 测试基础网络延迟（使用Socket.IO握手请求）
const testNetworkLatency = async () => {
  try {
    console.log('🌐 开始测试Socket.IO握手延迟...')
    const start = performance.now()

    // 使用Socket.IO的握手端点进行测试
    await fetch('http://localhost:8000/socket.io/?EIO=4&transport=polling', {
      method: 'GET',
    })

    const end = performance.now()
    const latency = end - start

    console.log(`🌐 Socket.IO握手延迟: ${latency.toFixed(2)} ms`)

    // 延迟分析
    if (latency > 200) {
      console.log('⚠️  握手延迟异常高 (>200ms)，可能原因：')
      console.log('   - 服务器刚启动，正在初始化')
      console.log('   - 服务器负载高或响应慢')
      console.log('   - 网络栈问题')
    } else if (latency > 50) {
      console.log('⚠️  握手延迟较高 (>50ms)，但在可接受范围')
    } else {
      console.log('✅ 握手延迟正常 (<50ms)')
    }
  } catch (error) {
    console.log('🌐 Socket.IO握手测试失败:', error.message)
    console.log('💡 建议检查后端Socket.IO服务器是否正常运行')
  }
}

// 多次测试Socket.IO握手延迟获得更准确的数据
const testMultipleLatency = async () => {
  const results = []
  console.log('🔄 开始多次Socket.IO握手测试...')

  for (let i = 0; i < 3; i++) {
    try {
      const start = performance.now()
      await fetch('http://localhost:8000/socket.io/?EIO=4&transport=polling', {
        method: 'GET',
      })
      const end = performance.now()
      const latency = end - start
      results.push(latency)
      console.log(`📊 握手测试 ${i + 1}/3: ${latency.toFixed(2)} ms`)
    } catch (error) {
      console.log(`❌ 握手测试 ${i + 1}/3 失败:`, error.message)
    }

    // 短暂延迟避免请求过于密集
    if (i < 2) await new Promise((resolve) => setTimeout(resolve, 100))
  }

  if (results.length > 0) {
    const avg = results.reduce((a, b) => a + b, 0) / results.length
    const min = Math.min(...results)
    const max = Math.max(...results)

    console.log('📈 Socket.IO握手延迟统计:')
    console.log(`   平均: ${avg.toFixed(2)} ms`)
    console.log(`   最小: ${min.toFixed(2)} ms`)
    console.log(`   最大: ${max.toFixed(2)} ms`)

    if (avg > 200) {
      console.log('⚠️  平均握手延迟异常高，建议检查服务器状态')
    } else if (avg < 50) {
      console.log('✅ Socket.IO服务器响应良好')
    }
  }
}

// 异步测试网络延迟
testNetworkLatency()
setTimeout(testMultipleLatency, 1000) // 1秒后进行多次测试

export const socket = io('http://localhost:8000', {
  transports: ['websocket', 'polling'],
  // 添加一些优化配置
  timeout: 5000,
  forceNew: true,
  // 优化连接参数
  upgrade: true,
  rememberUpgrade: true,
})

// 记录连接开始尝试的时间
let connectAttemptTime = null

// 监听连接开始尝试
socket.on('connect', () => {
  const connectTime = performance.now()
  const totalDuration = connectTime - socketCreateTime
  const connectDuration = connectAttemptTime
    ? connectTime - connectAttemptTime
    : null

  console.log('✅ Socket 连接成功!')
  console.log(`⏱️  总耗时(包含模块加载): ${totalDuration.toFixed(2)} ms`)
  if (connectDuration) {
    console.log(`🚀 纯连接耗时: ${connectDuration.toFixed(2)} ms`)
  }
  console.log('🕐 连接成功时间:', new Date().toISOString())
})

// 监听连接尝试开始
socket.on('connecting', () => {
  connectAttemptTime = performance.now()
  console.log('🔄 开始尝试连接...')
})

// 监听连接失败事件
socket.on('connect_error', (error) => {
  const errorTime = performance.now()
  const totalDuration = errorTime - socketCreateTime
  const connectDuration = connectAttemptTime
    ? errorTime - connectAttemptTime
    : null

  console.log('❌ Socket 连接失败!')
  console.log(`⏱️  总失败耗时: ${totalDuration.toFixed(2)} ms`)
  if (connectDuration) {
    console.log(`🚀 纯连接尝试耗时: ${connectDuration.toFixed(2)} ms`)
  }
  console.log('🔴 连接失败时间:', new Date().toISOString())
  console.log('📋 错误详情:', error)
})

// 监听重连尝试
socket.on('reconnect_attempt', (attemptNumber) => {
  connectAttemptTime = performance.now()
  console.log(`🔄 重连尝试 #${attemptNumber}`)
})

// 添加更详细的调试信息
socket.on('disconnect', (reason) => {
  console.log('🔌 Socket 断开连接:', reason)
})
