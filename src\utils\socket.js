import { io } from 'socket.io-client'

// 记录socket创建时间
const socketCreateTime = performance.now()
console.log('🔌 Socket 创建时间:', new Date().toISOString())

export const socket = io('http://localhost:8000', {
  transports: ['websocket', 'polling'],
})

// 监听连接成功事件，计算耗时
socket.on('connect', () => {
  const connectTime = performance.now()
  const duration = connectTime - socketCreateTime
  console.log('✅ Socket 连接成功!')
  console.log(`⏱️  连接耗时: ${duration.toFixed(2)} ms`)
  console.log('🕐 连接成功时间:', new Date().toISOString())
})

// 监听连接失败事件
socket.on('connect_error', (error) => {
  const errorTime = performance.now()
  const duration = errorTime - socketCreateTime
  console.log('❌ Socket 连接失败!')
  console.log(`⏱️  失败耗时: ${duration.toFixed(2)} ms`)
  console.log('🔴 连接失败时间:', new Date().toISOString())
  console.log('📋 错误详情:', error)
})
