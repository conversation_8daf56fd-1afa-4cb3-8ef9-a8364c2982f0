import { io } from 'socket.io-client'

export const socket = io('http://localhost:8000', {
  transports: ['websocket'],
  autoConnect: false,
})

// 延迟连接，等待页面关键资源加载完成
const connectWhenReady = () => {
  console.log('🔌 准备建立 Socket 连接...')
  console.log('📄 页面状态:', document.readyState)

  const startConnect = () => {
    const startTime = performance.now()
    console.log('🚀 开始连接 Socket...')

    socket.on('connect', () => {
      const connectTime = performance.now()
      const duration = connectTime - startTime
      console.log('✅ Socket 连接成功!')
      console.log(`⏱️  连接耗时: ${duration.toFixed(2)} ms`)
    })

    socket.connect()
  }

  if (document.readyState === 'complete') {
    // 页面已完全加载，稍微延迟一下让浏览器处理完其他任务
    setTimeout(startConnect, 1500)
  } else {
    // 等待页面完全加载
    window.addEventListener('load', () => {
      setTimeout(startConnect, 1500)
    })
  }
}

// 启动连接
connectWhenReady()
