#!/usr/bin/env python3
"""
量化回测Socket.IO服务器
提供前后端实时通信，支持策略选择、回测执行、实时数据推送等功能
"""

import asyncio
import logging
import os
import sys
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import socketio
import uvicorn

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from quantback.log_setup import setup_logging
from quantback.port_bt.engine import BacktestEngine

# 设置日志
logger = logging.getLogger(__name__)

# 创建 Socket.IO 服务器实例（使用 ASGI 模式）
sio = socketio.AsyncServer(
    async_mode='asgi', cors_allowed_origins='*', logger=True, engineio_logger=True
)
app = socketio.ASGIApp(sio)

# 线程池用于执行耗时的回测任务
executor = ThreadPoolExecutor(max_workers=2)

# 全局状态
backtest_status = {
    'is_running': False,
    'current_strategy': None,
    'start_date': None,
    'end_date': None,
    'progress': 0,
    'results': None,
}


def get_strategy_files() -> List[Dict[str, str]]:
    """获取策略文件列表"""
    strategies_dir = Path(__file__).parent / 'port_bt' / 'strategies'
    strategy_files = []

    if strategies_dir.exists():
        for py_file in strategies_dir.glob('*.py'):
            if py_file.name != '__init__.py':
                strategy_files.append(
                    {'filename': py_file.name, 'name': py_file.stem, 'path': str(py_file)}
                )

    return strategy_files


def load_strategy_module(strategy_path: str):
    """动态加载策略模块"""
    import importlib.util

    spec = importlib.util.spec_from_file_location('strategy', strategy_path)
    if spec is None or spec.loader is None:
        raise ImportError(f'无法加载策略文件: {strategy_path}')

    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)

    return module


async def run_backtest_async(strategy_path: str, start_date: str, end_date: str, sid: str):
    """异步运行回测"""
    try:
        # 更新状态
        backtest_status['is_running'] = True
        backtest_status['progress'] = 0
        await sio.emit('backtest_status', backtest_status, to=sid)

        # 在线程池中运行回测
        loop = asyncio.get_event_loop()
        results = await loop.run_in_executor(
            executor, run_backtest_sync, strategy_path, start_date, end_date, sid
        )

        # 回测完成
        backtest_status['is_running'] = False
        backtest_status['progress'] = 100
        backtest_status['results'] = results

        await sio.emit(
            'backtest_completed', {'status': backtest_status, 'results': results}, to=sid
        )

    except Exception as e:
        logger.error(f'回测执行错误: {e}')
        backtest_status['is_running'] = False
        await sio.emit('backtest_error', {'error': str(e), 'status': backtest_status}, to=sid)


def run_backtest_sync(strategy_path: str, start_date: str, end_date: str, sid: str):
    """同步运行回测（在线程池中执行）"""
    try:
        # 加载策略模块
        strategy_module = load_strategy_module(strategy_path)

        if not hasattr(strategy_module, 'initialize'):
            raise ValueError('策略文件必须包含initialize函数')

        if not hasattr(strategy_module, 'STRATEGY_CONFIG'):
            raise ValueError('策略文件必须包含STRATEGY_CONFIG配置')

        # 获取策略配置并更新日期
        strategy_config = strategy_module.STRATEGY_CONFIG.copy()
        strategy_config['start_date'] = start_date
        strategy_config['end_date'] = end_date

        # 创建回测引擎
        engine = BacktestEngine(
            initialize_func=strategy_module.initialize, strategy_config=strategy_config
        )

        # 运行回测
        results = engine.run()

        # 处理结果数据，转换为可序列化的格式
        processed_results = process_backtest_results(results)

        return processed_results

    except Exception as e:
        logger.error(f'回测同步执行错误: {e}')
        raise


def process_backtest_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """处理回测结果，转换为前端可用的格式"""
    try:
        processed = {}

        # 处理投资组合历史数据
        if 'portfolio_history' in results:
            portfolio_df = results['portfolio_history']

            # 处理日期索引 - 检查索引类型
            if hasattr(portfolio_df.index, 'strftime'):
                # 如果是DatetimeIndex
                dates = portfolio_df.index.strftime('%Y-%m-%d').tolist()
            else:
                # 如果是其他类型的索引，尝试转换为字符串
                dates = [str(date) for date in portfolio_df.index]

            processed['portfolio_history'] = {
                'dates': dates,
                'total_value': portfolio_df['total_value'].tolist(),
                'available_cash': portfolio_df['available_cash'].tolist(),
                'returns': portfolio_df['returns'].fillna(0).tolist(),
                'cumulative_returns': (portfolio_df['returns'].fillna(0).cumsum()).tolist(),
            }

        # 处理性能统计
        if 'performance_stats' in results:
            processed['performance_stats'] = results['performance_stats']

        # 处理交易记录
        if 'trade_records' in results:
            trade_records = results['trade_records']
            if trade_records:
                processed['trade_records'] = []
                for record in trade_records:
                    # 处理日期字段
                    date_value = record.get('date', '')
                    if hasattr(date_value, 'strftime'):
                        date_str = date_value.strftime('%Y-%m-%d')
                    elif hasattr(date_value, 'date'):
                        date_str = date_value.date().strftime('%Y-%m-%d')
                    else:
                        date_str = str(date_value)

                    processed['trade_records'].append(
                        {
                            'date': date_str,
                            'symbol': record.get('symbol', ''),
                            'action': record.get('action', ''),
                            'volume': record.get('volume', 0),
                            'price': record.get('price', 0),
                            'amount': record.get('amount', 0),
                            'commission': record.get('commission', 0),
                        }
                    )
            else:
                processed['trade_records'] = []

        # 处理最终持仓
        if 'final_positions' in results:
            positions = results['final_positions']
            processed['final_positions'] = [
                {
                    'symbol': symbol,
                    'volume': pos.total_volume,
                    'available_volume': pos.closeable_volume,
                    'avg_cost': pos.avg_cost,
                    'market_value': pos.value,
                }
                for symbol, pos in positions.items()
                if pos.total_volume > 0
            ]

        return processed

    except Exception as e:
        logger.error(f'处理回测结果错误: {e}')
        return {'error': str(e)}


@sio.event
async def connect(sid, environ):
    """客户端连接事件"""
    logger.info(f'客户端连接: {sid}')
    await sio.emit(
        'welcome', {'message': '连接成功！', 'server_time': datetime.now().isoformat()}, to=sid
    )


@sio.event
async def disconnect(sid):
    """客户端断开事件"""
    logger.info(f'客户端断开: {sid}')


@sio.event
async def get_strategies(sid, data=None):
    """获取策略文件列表"""
    try:
        strategies = get_strategy_files()
        await sio.emit('strategies_list', {'strategies': strategies}, to=sid)
    except Exception as e:
        logger.error(f'获取策略列表错误: {e}')
        await sio.emit('error', {'message': f'获取策略列表失败: {str(e)}'}, to=sid)


@sio.event
async def start_backtest(sid, data):
    """开始回测"""
    try:
        strategy_name = data.get('strategy')
        start_date = data.get('start_date')
        end_date = data.get('end_date')

        if not all([strategy_name, start_date, end_date]):
            await sio.emit('error', {'message': '缺少必要参数：策略、开始日期或结束日期'}, to=sid)
            return

        # 检查是否已有回测在运行
        if backtest_status['is_running']:
            await sio.emit('error', {'message': '已有回测任务在运行中'}, to=sid)
            return

        # 查找策略文件
        strategies = get_strategy_files()
        strategy_file = None
        for strategy in strategies:
            if strategy['name'] == strategy_name:
                strategy_file = strategy['path']
                break

        if not strategy_file:
            await sio.emit('error', {'message': f'未找到策略文件: {strategy_name}'}, to=sid)
            return

        # 更新状态
        backtest_status['current_strategy'] = strategy_name
        backtest_status['start_date'] = start_date
        backtest_status['end_date'] = end_date

        # 异步启动回测
        asyncio.create_task(run_backtest_async(strategy_file, start_date, end_date, sid))

        await sio.emit(
            'backtest_started', {'message': '回测已开始', 'status': backtest_status}, to=sid
        )

    except Exception as e:
        logger.error(f'启动回测错误: {e}')
        await sio.emit('error', {'message': f'启动回测失败: {str(e)}'}, to=sid)


@sio.event
async def get_backtest_status(sid, data=None):
    """获取回测状态"""
    await sio.emit('backtest_status', backtest_status, to=sid)


if __name__ == '__main__':
    # 设置日志
    setup_logging()

    print('=' * 60)
    print('QuantBack 回测服务器')
    print('=' * 60)
    print(f'启动时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print(f'服务地址: http://localhost:8001')
    print('=' * 60)

    # 启动服务器
    uvicorn.run(app, host='0.0.0.0', port=8000, log_level='info')
