"""
行情接口模块
大部分行情数据从本地数据库读取, 少部分可能转接其它数据源.
"""

import logging
from datetime import datetime
from typing import List, Optional, Union

import pandas as pd
from line_profiler import LineProfiler

from quantback.database.clickhouse_client import get_clickhouse_client

logger = logging.getLogger(__name__)
profiler = LineProfiler()  # 性能优化：使用profiler装饰器进行性能分析


def _resample_by_rows(df: pd.DataFrame, period: str) -> Optional[pd.DataFrame]:
    """
    按行数对k线数据进行重采样（用于多分钟周期）

    Args:
        df: 原始k线数据，已设置时间索引
        period: 目标周期，'3m', '5m', '15m', '30m', '60m'

    Returns:
        重采样后的k线数据
    """
    try:
        # 定义周期对应的行数
        period_rows = {'3m': 3, '5m': 5, '15m': 15, '30m': 30, '60m': 60}

        n_rows = period_rows.get(period)
        if n_rows is None:
            logger.error(f'不支持的分钟周期: {period}')
            return None

        # 重置索引以便按行数分组
        df_reset = df.reset_index()

        # 按每n行分组
        df_reset['group'] = df_reset.index // n_rows

        # 定义聚合规则
        agg_rules = {
            'datetime': 'last',  # 时间标签取最后一个（周期末尾）
            'stock_code': 'first',  # 股票代码取第一个
        }

        # 根据DataFrame中实际存在的列动态添加聚合规则
        field_agg_mapping = {
            'open': 'first',  # 开盘价取第一个
            'high': 'max',  # 最高价取最大值
            'low': 'min',  # 最低价取最小值
            'close': 'last',  # 收盘价取最后一个
            'pre_close': 'first',  # 前收盘价取第一个
            'volume': 'sum',  # 成交量求和
            'amount': 'sum',  # 成交额求和
            'high_limit': 'first',  # 涨停价取第一个
            'low_limit': 'first',  # 跌停价取第一个
        }

        # 只为DataFrame中存在的列添加聚合规则
        for col in df_reset.columns:
            if col in field_agg_mapping:
                agg_rules[col] = field_agg_mapping[col]

        # 按组聚合
        resampled_df = df_reset.groupby('group').agg(agg_rules)

        # 设置时间索引
        resampled_df.set_index('datetime', inplace=True)

        return resampled_df

    except Exception as e:
        logger.error(f'按行数重采样失败: {str(e)}')
        return None


def _resample_kline_data(df: pd.DataFrame, period: str) -> Optional[pd.DataFrame]:
    """
    对k线数据进行重采样

    Args:
        df: 原始k线数据，已设置时间索引
        period: 目标周期，'week'(周线), 'month'(月线), 'quarter'(季线), 'year'(年线)

    Returns:
        重采样后的k线数据
    """
    try:
        # 定义重采样规则
        agg_rules = {
            'datetime': 'last',  # 时间标签取最后一个（实际数据的最后一条）
            'stock_code': 'first',  # 股票代码取第一个
        }

        # 根据DataFrame中实际存在的列动态添加聚合规则
        field_agg_mapping = {
            'open': 'first',  # 开盘价取第一个
            'high': 'max',  # 最高价取最大值
            'low': 'min',  # 最低价取最小值
            'close': 'last',  # 收盘价取最后一个
            'pre_close': 'first',  # 前收盘价取第一个
            'volume': 'sum',  # 成交量求和
            'amount': 'sum',  # 成交额求和
            'high_limit': 'first',  # 涨停价取第一个
            'low_limit': 'first',  # 跌停价取第一个
        }

        # 只为DataFrame中存在的列添加聚合规则
        for col in df.columns:
            if col in field_agg_mapping:
                agg_rules[col] = field_agg_mapping[col]

        # 将自定义周期名称映射到pandas resample参数
        # 使用新的pandas推荐参数，避免FutureWarning
        period_mapping = {
            'week': 'W',  # 周线，使用'W'而不是已弃用的'w'
            'month': 'ME',  # 月线，使用'ME'而不是已弃用的'M'
            'quarter': 'QE',  # 季线，使用'QE'而不是已弃用的'Q'
            'year': 'YE',  # 年线，使用'YE'而不是已弃用的'Y'
        }

        pandas_period = period_mapping.get(period, period)

        # 复制出一列, 用于取该周期实际数据的最后一天, 而不是自然周期的最后一天
        df['datetime'] = df.index
        resampled_df = df.resample(pandas_period).agg(agg_rules)

        # 删除没有数据的行（全为NaN的行）, 对应着没有数据的自然周期
        # 使用实际存在的价格字段进行dropna检查
        price_cols_to_check = [
            col for col in ['open', 'high', 'low', 'close'] if col in resampled_df.columns
        ]
        if price_cols_to_check:
            resampled_df = resampled_df.dropna(subset=price_cols_to_check)

        # 使用实际数据的最后一条记录的时间作为索引
        resampled_df.set_index('datetime', inplace=True)

        return resampled_df

    except Exception as e:
        logger.error(f'重采样失败: {str(e)}')
        return None


def get_trading_calendar(
    start_date: Optional[Union[str, datetime]] = None,
    end_date: Optional[Union[str, datetime]] = None,
    include_prev_day: bool = False,
) -> List[str]:
    """
    获取交易日历

    Args:
        start_date: 开始日期，支持字符串格式 'YYYY-MM-DD' 或 datetime 对象。传None或空字符串''表示不限制开始时间，默认None
        end_date: 结束日期，支持字符串格式 'YYYY-MM-DD' 或 datetime 对象。传None或空字符串''表示不限制结束时间，默认None
        include_prev_day: 是否包含start_date的前一个交易日，默认False

    Returns:
        List[str]: 交易日列表 (格式: 'YYYY-MM-DD')
    """
    try:
        # 处理datetime类型的start_date和end_date
        if isinstance(start_date, datetime):
            start_date = start_date.strftime('%Y-%m-%d')

        if isinstance(end_date, datetime):
            end_date = end_date.strftime('%Y-%m-%d')

        client = get_clickhouse_client()

        # 构建WHERE条件
        where_conditions = []
        parameters = {}

        if include_prev_day and start_date:
            # 包含前一天的SQL：查询start_date前一个交易日到end_date的所有交易日
            where_conditions.append("""trade_date >= (
                SELECT MAX(trade_date)
                FROM trading_calendar
                WHERE trade_date < %(start_date)s
            )""")
            parameters['start_date'] = start_date
        elif start_date:
            # 原有的SQL：只查询start_date到end_date的交易日
            where_conditions.append('trade_date >= %(start_date)s')
            parameters['start_date'] = start_date

        if end_date:
            where_conditions.append('trade_date <= %(end_date)s')
            parameters['end_date'] = end_date

        # 构建完整的SQL
        sql = 'SELECT trade_date FROM trading_calendar'
        if where_conditions:
            sql += ' WHERE ' + ' AND '.join(where_conditions)
        sql += ' ORDER BY trade_date'

        df = client.query_df(sql, parameters=parameters)

        # 返回 YYYY-MM-DD 格式
        trade_dates = df['trade_date'].dt.strftime('%Y-%m-%d').tolist()
        return sorted(trade_dates)
    except Exception as e:
        logger.error(f'获取交易日历失败: {e}')
        return []


# @profiler  # 性能优化：使用profiler装饰器进行性能分析
def get_kline_data(
    stock_code: str,
    start_date: Optional[Union[str, datetime]] = None,
    end_date: Optional[Union[str, datetime]] = None,
    count: int = 0,
    period: str = '1d',
    adjust_type: str = 'pre',
    fields: Optional[List[str]] = None,
    start_inclusive: bool = True,
) -> Optional[pd.DataFrame]:
    """
    从ClickHouse数据库查询k线数据

    Args:
        stock_code: 股票代码，如 '000001.SZ'
        start_date: 开始日期，支持字符串格式 'YYYY-MM-DD'(日线) 或 'YYYY-MM-DD HH:MM:SS'(分钟线)，或 datetime 对象，当count>0时忽略。传None或空字符串''表示不限制开始时间，默认None
        end_date: 结束日期，支持字符串格式 'YYYY-MM-DD'(日线) 或 'YYYY-MM-DD HH:MM:SS'(分钟线)，或 datetime 对象。传None或空字符串''表示不限制结束时间，默认None
        period: 数据周期，'1d'(日线), '1m'(1分钟线), '3m'(3分钟线), '5m'(5分钟线), '15m'(15分钟线), '30m'(30分钟线), '60m'(60分钟线), 'week'(周线), 'month'(月线), 'quarter'(季线), 'year'(年线)
        adjust_type: 复权类型，'none'(不复权), 'pre'(前复权), 'post'(后复权). 后复权相对于上市首日, 前复权相对于end_date.
        count: 获取最近的记录数，当count>0时忽略start_date参数
        start_inclusive: 查询范围是否包含start_date本身，默认True
        fields: 需要查询的字段列表，传None则查询所有字段。可选字段包括: open, high, low, close, pre_close, volume, amount, high_limit, low_limit。时间列和stock_code始终包含。

    Returns:
        pd.DataFrame: k线数据，包含指定的字段列
                     如果查询失败或无数据则返回None
    """
    try:
        # 处理datetime类型的start_date和end_date
        if isinstance(start_date, datetime):
            if period.endswith('m'):
                start_date = start_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                start_date = start_date.strftime('%Y-%m-%d')

        if isinstance(end_date, datetime):
            if period.endswith('m'):
                end_date = end_date.strftime('%Y-%m-%d %H:%M:%S')
            else:
                end_date = end_date.strftime('%Y-%m-%d')

        # 获取ClickHouse客户端
        client = get_clickhouse_client()

        # 定义可选字段
        optional_fields = [
            'open',
            'high',
            'low',
            'close',
            'pre_close',
            'volume',
            'amount',
            'high_limit',
            'low_limit',
        ]

        # 构建SELECT字段列表
        if fields is None:
            # 如果fields为None，只查询['close']
            select_fields = ['close']
        else:
            # 如果指定了fields，只查询指定的字段（过滤掉不存在的字段）
            select_fields = [field for field in fields if field in optional_fields]

        # 根据周期选择表名和时间字段
        # 对于周线、月线、季线、年线，都基于日线数据进行重采样
        # 对于多分钟周期，都基于1分钟数据进行重采样
        if period in ['1d', 'week', 'month', 'quarter', 'year']:
            table_name = 'k_day'
            time_field = 'trade_date'
        elif period in ['1m', '3m', '5m', '15m', '30m', '60m']:
            table_name = 'k_1m'
            time_field = 'trade_time'
        else:
            logger.error(f'不支持的数据周期: {period}')
            return None

        # 当count>0时，计算需要查询的基础数据量
        if count > 0:
            if period in ['3m', '5m', '15m', '30m', '60m']:
                # 对于多分钟周期，需要查询 period_minutes * count 条1分钟数据
                period_minutes = {'3m': 3, '5m': 5, '15m': 15, '30m': 30, '60m': 60}
                base_count = period_minutes[period] * count
            elif period in ['week', 'month', 'quarter', 'year']:
                # 对于长周期，预估需要的日线数据量（保守估计）
                period_days = {'week': 7, 'month': 31, 'quarter': 93, 'year': 366}
                base_count = period_days[period] * count
            else:
                # 对于1d和1m，直接使用count
                base_count = count
        else:
            base_count = 0

        # 先查询原始k线数据
        if count > 0:
            # 当count>0时，查询最近的base_count条记录
            where_conditions = ['stock_code = %(stock_code)s']
            parameters = {'stock_code': stock_code, 'base_count': base_count}

            if end_date:
                where_conditions.append(f'{time_field} <= %(end_date)s')
                parameters['end_date'] = end_date

            # 构建SELECT字段字符串
            select_fields_str = f'{time_field} as datetime, stock_code'
            if select_fields:
                select_fields_str += ', ' + ', '.join(select_fields)

            kline_sql = f"""
            SELECT
                {select_fields_str}
            FROM {table_name}
            WHERE {' AND '.join(where_conditions)}
            ORDER BY {time_field} DESC
            LIMIT %(base_count)s
            """
            kline_result = client.query_df(kline_sql, parameters=parameters)
            # 反转结果，使得最新的数据在最前面
            if not kline_result.empty:
                kline_result = kline_result.iloc[::-1].reset_index(drop=True)
        else:
            # 当count=0时，使用原有的时间范围查询
            where_conditions = ['stock_code = %(stock_code)s']
            parameters = {'stock_code': stock_code}

            if start_date:
                start_op = '>=' if start_inclusive else '>'  # 根据参数选择操作符
                where_conditions.append(f'{time_field} {start_op} %(start_date)s')
                parameters['start_date'] = start_date

            if end_date:
                where_conditions.append(f'{time_field} <= %(end_date)s')
                parameters['end_date'] = end_date

            # 构建SELECT字段字符串
            select_fields_str = f'{time_field} as datetime, stock_code'
            if select_fields:
                select_fields_str += ', ' + ', '.join(select_fields)

            kline_sql = f"""
            SELECT
                {select_fields_str}
            FROM {table_name}
            WHERE {' AND '.join(where_conditions)}
            ORDER BY {time_field}
            """
            kline_result = client.query_df(kline_sql, parameters=parameters)
        if kline_result.empty:
            logger.warning(f'未查询到k线数据: {stock_code}, {start_date} ~ {end_date}')
            return None
        # volume字段转int类型
        if 'volume' in kline_result.columns:
            kline_result['volume'] = kline_result['volume'].astype(int)

        # 判断是否需要复权计算
        need_adjust = adjust_type in ['pre', 'post']

        # 优化：某些情况下可以跳过复权计算
        if need_adjust:
            # 情况1：count=1时只查询一条记录，不需要复权（日线和分钟线）
            if count == 1 and (period == '1d' or period.endswith('m')):
                need_adjust = False
                logger.debug('跳过复权计算：count=1，只查询一条记录')

            # 情况2：start_date和end_date是同一天，不需要复权
            elif start_date and end_date:
                # 提取日期部分进行比较（取前10位：YYYY-MM-DD）
                start_date_only = start_date[:10]
                end_date_only = end_date[:10]

                if start_date_only == end_date_only:
                    need_adjust = False
                    logger.debug(f'跳过复权计算：查询同一天数据 {start_date_only}')

        if need_adjust:
            # 查询复权因子数据
            dividend_where_conditions = ['stock_code = %(stock_code)s']
            dividend_parameters = {'stock_code': stock_code}

            if end_date:
                # 需要使用toDate转换因为end_date可能含时间
                dividend_where_conditions.append('ex_date <= toDate(%(end_date)s)')
                dividend_parameters['end_date'] = end_date

            dividend_sql = f"""
            SELECT
                ex_date,
                dr
            FROM dividend_info
            WHERE {' AND '.join(dividend_where_conditions)}
            ORDER BY ex_date
            """

            # 查询复权因子并在Python中计算
            dividend_result = client.query_df(dividend_sql, parameters=dividend_parameters)

            if not dividend_result.empty:
                # 处理时间类型匹配问题：将ex_date转换为当日9:00:00的时间戳
                if period in ['1m', '3m', '5m', '15m', '30m', '60m']:
                    # 对于分钟数据，将ex_date转换为当日9:31:00，并添加时区信息
                    dividend_result['ex_date_time'] = pd.to_datetime(
                        dividend_result['ex_date'].astype(str) + ' 09:31:00'
                    ).dt.tz_localize('Asia/Shanghai')
                else:
                    # 对于日线数据，直接使用ex_date
                    dividend_result['ex_date_time'] = dividend_result['ex_date']

                if adjust_type == 'post':
                    dividend_result['adjust_factor'] = dividend_result['dr'].cumprod()
                    # 找到ex_date早于k线数据第一个日期的最后一个复权因子
                    mask = dividend_result['ex_date_time'] < kline_result['datetime'].iloc[0]
                    last_factor = (
                        dividend_result.loc[mask, 'adjust_factor'].iloc[-1] if mask.any() else 1
                    )

                    kline_result = kline_result.merge(
                        dividend_result[['ex_date_time', 'adjust_factor']],
                        left_on='datetime',
                        right_on='ex_date_time',
                        how='left',
                    )
                    kline_result['adjust_factor'] = (
                        kline_result['adjust_factor'].ffill().fillna(last_factor)
                    )

                else:
                    dividend_result['adjust_factor'] = (
                        1 / dividend_result['dr'][::-1].cumprod()[::-1]
                    )

                    kline_result = kline_result.merge(
                        dividend_result[['ex_date_time', 'adjust_factor']],
                        left_on='datetime',
                        right_on='ex_date_time',
                        how='left',
                    )
                    kline_result['adjust_factor'] = (
                        kline_result['adjust_factor'].shift(-1).bfill().fillna(1)
                    )

                # 应用复权因子
                price_columns = [
                    'open',
                    'high',
                    'low',
                    'close',
                    'pre_close',
                    'high_limit',
                    'low_limit',
                ]
                # 只对实际存在的价格列进行复权
                for col in price_columns:
                    if col in kline_result.columns:
                        kline_result[col] = kline_result[col] * kline_result['adjust_factor']

                # 删除临时列
                kline_result.drop(['adjust_factor', 'ex_date_time'], axis=1, inplace=True)

        # 设置时间索引
        kline_result.set_index('datetime', inplace=True)

        # 对于周线、月线、季线、年线，在复权计算完成后进行时间重采样
        if period in ['week', 'month', 'quarter', 'year']:
            kline_result = _resample_kline_data(kline_result, period)
            if kline_result is None:
                logger.warning(f'时间重采样失败: {stock_code}, period: {period}')
                return None

        # 对于多分钟周期，在复权计算完成后进行行数重采样
        elif period in ['3m', '5m', '15m', '30m', '60m']:
            kline_result = _resample_by_rows(kline_result, period)
            if kline_result is None:
                logger.warning(f'行数重采样失败: {stock_code}, period: {period}')
                return None

        # 如果指定了count，截取最后count条记录
        if count > 0 and len(kline_result) > count:
            kline_result = kline_result.tail(count)

        logger.info(f'成功查询到 {len(kline_result)} 条k线数据: {stock_code}')

        # 去除index的时区信息, 而时间值不变, 例如2025-01-06 09:30:00+08:00 -> 2025-01-06 09:30:00
        kline_result.index = kline_result.index.tz_localize(None)
        return kline_result

    except Exception as e:
        logger.error(f'查询k线数据失败: {str(e)}')
        return None


def get_dividend_info(stock_codes: List[str], ex_date: str) -> Optional[pd.DataFrame]:
    """
    获取指定股票在指定除权日的分红送转信息

    Args:
        stock_codes: 股票代码列表
        ex_date: 除权日，格式 'YYYY-MM-DD'

    Returns:
        pd.DataFrame: 分红送转数据，包含 stock_code, interest, stock_bonus, stock_gift, allot_num, allot_price 等列
                     如果查询失败或无数据则返回None
    """
    try:
        if not stock_codes:
            return None

        client = get_clickhouse_client()

        # 构建股票代码的IN条件
        stock_codes_str = "', '".join(stock_codes)

        sql = f"""
        SELECT
            stock_code,
            ex_date,
            interest,
            stock_bonus,
            stock_gift,
            allot_num,
            allot_price
        FROM dividend_info
        WHERE stock_code IN ('{stock_codes_str}')
            AND ex_date = %(ex_date)s
        ORDER BY stock_code
        """

        df = client.query_df(sql, parameters={'ex_date': ex_date})

        if df.empty:
            return None

        logger.info(f'查询到 {len(df)} 条分红送转数据，除权日: {ex_date}')
        return df

    except Exception as e:
        logger.error(f'查询分红送转数据失败: {str(e)}')
        return None


if __name__ == '__main__':
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)

    # 测试日线数据
    print('=== 日线数据 ===')
    df_daily = get_kline_data(
        '600519.SH', '2024-12-01', '2025-01-06', period='1d', adjust_type='pre'
    )
    print(df_daily)

    # 测试周线数据
    print('\n=== 周线数据 ===')
    df_weekly = get_kline_data(
        '600519.SH', '2024-12-01', '2025-01-06', period='week', adjust_type='pre'
    )
    print(df_weekly)

    # 测试月线数据
    print('\n=== 月线数据 ===')
    df_monthly = get_kline_data(
        '600519.SH', '2024-01-01', '2025-01-06', period='month', adjust_type='pre'
    )
    print(df_monthly)

    # 测试季线数据
    print('\n=== 季线数据 ===')
    df_quarterly = get_kline_data(
        '600519.SH', '2024-01-01', '2025-01-06', period='quarter', adjust_type='pre'
    )
    print(df_quarterly)

    # 测试5分钟线数据
    print('\n=== 5分钟线数据 ===')
    df_5m = get_kline_data(
        '600519.SH', '2024-12-18 09:30:00', '2024-12-18 11:30:00', period='5m', adjust_type='pre'
    )
    print(df_5m)

    # 测试15分钟线数据
    print('\n=== 15分钟线数据 ===')
    df_15m = get_kline_data(
        '600519.SH', '2024-12-18 09:30:00', '2024-12-18 15:00:00', period='15m', adjust_type='pre'
    )
    print(df_15m)

    # 测试count功能
    print('\n=== 测试count功能：最近5条日线数据 ===')
    df_count_daily = get_kline_data(
        '600519.SH', '', '2025-01-06', period='1d', adjust_type='pre', count=5
    )
    print(df_count_daily)

    print('\n=== 测试count功能：最近3条5分钟线数据 ===')
    df_count_5m = get_kline_data(
        '600519.SH', '', '2024-12-18 15:00:00', period='5m', adjust_type='pre', count=3
    )
    print(df_count_5m)

    print('\n=== 测试count功能：最近2条周线数据 ===')
    df_count_weekly = get_kline_data(
        '600519.SH', '', '2025-01-06', period='week', adjust_type='pre', count=2
    )
    print(df_count_weekly)

    # # 测试1分钟线数据
    # df = get_kline_data(
    #     '600519.SH', '2024-12-18 00:00:00', '2025-01-06 23:59:59', period='1m', adjust_type='pre'
    # )
