# Socket.IO 测试服务器

这是一个独立的 Socket.IO 测试服务器，使用 Gunicorn + Uvicorn 运行，专门用于测试连接性能。

## 文件结构

```
├── test_socket_server.py      # 主服务器文件
├── gunicorn_config.py         # Gunicorn 配置文件
├── start_test_server.py       # 启动脚本
├── test_server_requirements.txt # 依赖文件
└── TEST_SERVER_README.md      # 说明文档
```

## 安装依赖

```bash
pip install -r test_server_requirements.txt
```

或者单独安装：

```bash
pip install gunicorn uvicorn[standard] python-socketio
```

## 启动服务器

### 方式1：使用启动脚本（推荐）

```bash
python start_test_server.py
```

### 方式2：直接使用 Gunicorn

```bash
gunicorn test_socket_server:app -c gunicorn_config.py
```

### 方式3：手动指定参数

```bash
gunicorn test_socket_server:app \
  -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8001 \
  --workers 1 \
  --log-level info
```

## 服务信息

- **地址**: http://localhost:8001
- **端口**: 8001
- **Worker**: 1个 (Socket.IO 推荐)
- **协议**: WebSocket + HTTP Long Polling

## 支持的事件

### 服务器事件

- `connect` - 客户端连接
- `disconnect` - 客户端断开
- `ping` - Ping-Pong 测试
- `get_stats` - 获取服务器统计
- `echo` - 回声测试

### 客户端接收事件

- `welcome` - 连接成功消息
- `pong` - Ping 响应
- `stats_response` - 统计信息响应
- `echo_response` - 回声响应

## 前端测试

修改 `src/utils/socket.js` 中的连接地址：

```javascript
export const socket = io('http://localhost:8001', {
  transports: ['websocket'],
  autoConnect: false,
})
```

## 性能测试

### 连接延迟测试

1. 启动测试服务器
2. 修改前端连接地址为 8001 端口
3. 刷新前端页面
4. 观察控制台输出的连接时间

### Ping-Pong 测试

```javascript
// 在浏览器控制台执行
socket.emit('ping')
socket.on('pong', (data) => {
  console.log('Pong received:', data)
})
```

### 统计信息测试

```javascript
// 获取服务器统计
socket.emit('get_stats')
socket.on('stats_response', (stats) => {
  console.log('Server stats:', stats)
})
```

## 日志输出

服务器会输出详细的连接日志：

```
[14:30:25.123] 客户端连接: abc123
当前连接数: 1
[14:30:30.456] 客户端断开: abc123
当前连接数: 0
```

## 停止服务器

按 `Ctrl+C` 停止服务器。

## 故障排除

### 端口被占用

如果 8001 端口被占用，修改 `gunicorn_config.py` 中的 `bind` 配置：

```python
bind = "0.0.0.0:8002"  # 改为其他端口
```

### 依赖问题

确保安装了所有必需的依赖：

```bash
pip list | grep -E "(gunicorn|uvicorn|socketio)"
```

### 连接问题

1. 检查防火墙设置
2. 确认端口未被其他程序占用
3. 检查前端连接地址是否正确
