"""
quantback.port_bt 模块专用日志配置
"""

import logging
import sys

from quantback.log_setup import ColorizedFormatter


def setup_port_bt_logger(log_level: int = logging.INFO):
    """
    设置 quantback.port_bt 模块的专用logger

    Returns:
        logging.Logger: 配置好的logger实例
    """
    # 创建专用logger
    logger = logging.getLogger('quantback.port_bt')

    # 防止重复添加handler
    if logger.handlers:
        return logger

    # 设置日志级别
    logger.setLevel(log_level)

    # 创建console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    # 设置格式化器
    console_handler.setFormatter(ColorizedFormatter('[%(colored_levelname)s] %(message)s'))

    # 添加handler到logger
    logger.addHandler(console_handler)

    # 不向上层传递日志
    logger.propagate = False

    return logger
